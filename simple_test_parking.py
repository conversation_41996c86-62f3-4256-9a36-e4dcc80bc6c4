#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试改进后的停车奖励函数
"""

import warnings
import os
warnings.filterwarnings('ignore')
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

import numpy as np
import sys
sys.path.append('.')

def test_reward_function():
    """直接测试奖励函数改进"""
    print("=== 测试改进后的停车奖励函数 ===")
    
    try:
        # 直接导入自定义环境
        from algorithms.environments.single_agent_env.custom_parking import CustomParkingEnv
        
        # 创建简单配置
        class SimpleConfig:
            def __init__(self):
                self.env_seed = 42
                self.max_episode_steps = 150
                self.render_mode = 'rgb_array'
        
        config = SimpleConfig()
        
        # 创建环境
        print("创建停车环境...")
        env = CustomParkingEnv(config)
        print("✅ 环境创建成功！")
        
        # 测试重置
        print("重置环境...")
        obs, info = env.reset()
        print(f"✅ 环境重置成功！观察空间维度: {obs.shape if hasattr(obs, 'shape') else len(obs)}")
        
        # 测试奖励函数改进
        print("\n=== 测试奖励函数组件 ===")
        
        # 模拟不同距离和动作的奖励
        test_actions = [
            ([0.5, 0.0], "正常前进"),
            ([0.1, 0.0], "慢速前进"), 
            ([-0.2, 0.0], "轻微刹车"),
            ([-0.5, 0.0], "强力刹车"),
            ([0.0, 0.0], "停止")
        ]
        
        print("测试不同动作的奖励...")
        for action, desc in test_actions:
            try:
                obs, reward, terminated, truncated, info = env.step(np.array(action))
                
                # 获取距离信息
                distance_to_goal = "未知"
                if hasattr(env, '_get_distance_to_goal'):
                    try:
                        distance_to_goal = f"{env._get_distance_to_goal():.2f}m"
                    except:
                        pass
                
                # 获取奖励分解
                reward_breakdown = "无详细信息"
                if isinstance(info, dict) and 'reward_breakdown' in info:
                    breakdown = info['reward_breakdown']
                    reward_breakdown = f"基础:{breakdown.get('base_reward', 0):.3f}, 路径:{breakdown.get('path_reward', 0):.3f}, 平滑:{breakdown.get('smoothness_reward', 0):.3f}"
                
                print(f"动作: {desc:8} | 奖励: {reward:6.3f} | 距离: {distance_to_goal:8} | {reward_breakdown}")
                
            except Exception as e:
                print(f"动作 {desc} 测试失败: {e}")
        
        # 测试多个步骤
        print(f"\n=== 测试连续步骤 ===")
        total_reward = 0
        
        for step in range(10):
            # 使用随机动作
            action = env.action_space.sample()
            
            # 在接近目标时使用刹车动作
            try:
                if hasattr(env, '_get_distance_to_goal'):
                    distance = env._get_distance_to_goal()
                    if distance < 5.0:
                        action[0] = -0.3  # 刹车
                        print(f"步骤 {step}: 距离目标 {distance:.2f}m，使用刹车")
            except:
                pass
            
            obs, reward, terminated, truncated, info = env.step(action)
            total_reward += reward
            
            if terminated or truncated:
                print(f"回合结束于步骤 {step}")
                break
        
        print(f"总奖励: {total_reward:.3f}")
        
        # 测试奖励函数的关键方法
        print(f"\n=== 测试奖励函数方法 ===")
        
        # 检查新增的方法是否存在
        methods_to_check = [
            '_get_distance_to_goal',
            '_compute_parking_precision_reward', 
            '_compute_parking_success_bonus'
        ]
        
        for method_name in methods_to_check:
            if hasattr(env, method_name):
                print(f"✅ {method_name} 方法存在")
                try:
                    if method_name == '_get_distance_to_goal':
                        result = getattr(env, method_name)()
                        print(f"   当前距离目标: {result:.2f}m")
                    elif method_name in ['_compute_parking_precision_reward', '_compute_parking_success_bonus']:
                        result = getattr(env, method_name)(np.array([0.0, 0.0]))
                        print(f"   测试奖励: {result:.3f}")
                except Exception as e:
                    print(f"   ⚠️ 方法调用失败: {e}")
            else:
                print(f"❌ {method_name} 方法不存在")
        
        print(f"\n✅ 测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_improvements():
    """分析改进内容"""
    print("\n=== 奖励函数改进分析 ===")
    print("1. 距离感知速度控制:")
    print("   - 3米内: 期望停止 (0.0-0.15)")
    print("   - 5米内: 期望慢速 (0.0-0.3)")
    print("   - 8米内: 期望中速 (0.1-0.5)")
    
    print("\n2. 停车精度奖励:")
    print("   - 2米内强烈鼓励停车")
    print("   - 4米内鼓励减速")
    print("   - 6米内适度减速")
    
    print("\n3. 成功停车奖励:")
    print("   - 1.5米内且低速: 额外奖励")
    print("   - 0.8米内: 1.0 大额奖励")
    print("   - 1.2米内: 0.5 中等奖励")
    
    print("\n4. 权重重新分配:")
    print("   - 基础奖励: 25%")
    print("   - 路径跟踪: 30%") 
    print("   - 平滑驾驶: 25%")
    print("   - 停车精度: 20%")
    print("   - 成功停车: 额外奖励")

if __name__ == "__main__":
    print("改进后的停车奖励函数测试")
    
    analyze_improvements()
    
    success = test_reward_function()
    
    if success:
        print("\n🎉 测试成功！奖励函数改进已生效")
        print("\n建议下一步:")
        print("1. 重新训练智能体")
        print("2. 观察训练过程中的奖励变化")
        print("3. 测试智能体是否学会刹车停车")
    else:
        print("\n❌ 测试失败，请检查环境配置")
