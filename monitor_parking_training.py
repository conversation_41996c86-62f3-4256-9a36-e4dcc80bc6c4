#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
监控停车训练过程，观察奖励函数改进效果
"""

import warnings
import os
warnings.filterwarnings('ignore')
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

import numpy as np
import matplotlib.pyplot as plt
import json
import time
from pathlib import Path

def monitor_training_progress():
    """监控训练进度"""
    print("=== 停车训练监控器 ===")
    print("监控改进后奖励函数的训练效果")
    
    # 监控的关键指标
    metrics = {
        'episode_rewards': [],
        'final_distances': [],
        'success_rates': [],
        'parking_precision_rewards': [],
        'success_bonus_rewards': [],
        'timestamps': []
    }
    
    log_dir = Path("./result/train/logs/")
    
    print(f"监控日志目录: {log_dir}")
    print("开始监控训练过程...")
    print("按 Ctrl+C 停止监控")
    
    try:
        while True:
            # 这里可以添加实际的日志读取逻辑
            # 目前提供模拟数据作为示例
            
            # 模拟训练数据
            current_time = time.time()
            
            # 模拟奖励改进效果
            episode_reward = np.random.normal(-0.5, 0.2)  # 模拟逐渐改善的奖励
            final_distance = max(0.5, np.random.exponential(3.0))  # 模拟距离逐渐减小
            success_rate = min(1.0, max(0.0, np.random.beta(2, 5)))  # 模拟成功率提升
            
            metrics['episode_rewards'].append(episode_reward)
            metrics['final_distances'].append(final_distance)
            metrics['success_rates'].append(success_rate)
            metrics['timestamps'].append(current_time)
            
            # 保持最近100个数据点
            for key in ['episode_rewards', 'final_distances', 'success_rates', 'timestamps']:
                if len(metrics[key]) > 100:
                    metrics[key] = metrics[key][-100:]
            
            # 计算统计信息
            if len(metrics['episode_rewards']) >= 10:
                recent_rewards = metrics['episode_rewards'][-10:]
                recent_distances = metrics['final_distances'][-10:]
                recent_success = metrics['success_rates'][-10:]
                
                avg_reward = np.mean(recent_rewards)
                avg_distance = np.mean(recent_distances)
                avg_success = np.mean(recent_success)
                
                print(f"\r最近10回合 - 平均奖励: {avg_reward:.3f}, "
                      f"平均最终距离: {avg_distance:.2f}m, "
                      f"平均成功率: {avg_success:.2f}", end="")
            
            time.sleep(2)  # 每2秒更新一次
            
    except KeyboardInterrupt:
        print("\n\n监控停止")
        
        # 生成监控报告
        generate_monitoring_report(metrics)

def generate_monitoring_report(metrics):
    """生成监控报告"""
    print("\n=== 训练监控报告 ===")
    
    if len(metrics['episode_rewards']) == 0:
        print("没有收集到训练数据")
        return
    
    # 计算改进趋势
    rewards = np.array(metrics['episode_rewards'])
    distances = np.array(metrics['final_distances'])
    success_rates = np.array(metrics['success_rates'])
    
    print(f"总监控回合数: {len(rewards)}")
    print(f"平均回合奖励: {np.mean(rewards):.3f} ± {np.std(rewards):.3f}")
    print(f"平均最终距离: {np.mean(distances):.2f}m ± {np.std(distances):.2f}m")
    print(f"平均成功率: {np.mean(success_rates):.2f} ± {np.std(success_rates):.2f}")
    
    # 分析改进趋势
    if len(rewards) >= 20:
        early_rewards = rewards[:len(rewards)//2]
        late_rewards = rewards[len(rewards)//2:]
        
        early_distances = distances[:len(distances)//2]
        late_distances = distances[len(distances)//2:]
        
        reward_improvement = np.mean(late_rewards) - np.mean(early_rewards)
        distance_improvement = np.mean(early_distances) - np.mean(late_distances)
        
        print(f"\n=== 改进趋势分析 ===")
        print(f"奖励改进: {reward_improvement:+.3f}")
        print(f"距离改进: {distance_improvement:+.2f}m")
        
        if reward_improvement > 0:
            print("✅ 奖励呈上升趋势，训练效果良好")
        else:
            print("⚠️ 奖励未明显改善，可能需要调整参数")
            
        if distance_improvement > 0:
            print("✅ 最终距离在减小，停车精度在提升")
        else:
            print("⚠️ 停车精度未明显改善")

def analyze_reward_components():
    """分析奖励组件的预期效果"""
    print("\n=== 奖励组件分析 ===")
    
    print("改进后的奖励函数应该产生以下效果:")
    print("\n1. 距离感知速度控制:")
    print("   - 远离目标(>8m): 正常前进，适中奖励")
    print("   - 接近目标(5-8m): 开始减速，获得减速奖励")
    print("   - 靠近目标(3-5m): 明显减速，获得更多奖励")
    print("   - 非常接近(<3m): 停车或微调，获得最高奖励")
    
    print("\n2. 停车精度奖励:")
    print("   - 在目标2米内: 强烈鼓励停车行为")
    print("   - 在目标4米内: 鼓励减速准备")
    print("   - 在目标6米内: 适度减速奖励")
    
    print("\n3. 成功停车奖励:")
    print("   - 1.5米内且低速: 额外奖励")
    print("   - 0.8米内精确停车: 1.0大额奖励")
    print("   - 1.2米内较精确: 0.5中等奖励")
    
    print("\n4. 预期训练改进:")
    print("   - 初期: 学会跟随路径到达目标附近")
    print("   - 中期: 开始在接近目标时减速")
    print("   - 后期: 学会精确停车在目标位置")

def provide_training_suggestions():
    """提供训练建议"""
    print("\n=== 训练建议 ===")
    
    print("1. 训练参数建议:")
    print("   - 增加训练步数到 1,000,000 步")
    print("   - 使用较小的学习率 0.0003")
    print("   - 增加并行环境数到 32")
    
    print("\n2. 监控关键指标:")
    print("   - 平均回合奖励是否稳步上升")
    print("   - 最终距离目标的距离是否减小")
    print("   - 停车精度奖励是否开始生效")
    print("   - 成功停车奖励的触发频率")
    
    print("\n3. 如果训练效果不佳:")
    print("   - 增加停车精度奖励权重(20% -> 30%)")
    print("   - 调整距离阈值(更宽松的停车条件)")
    print("   - 增加成功停车的额外奖励")
    print("   - 检查环境的终止条件设置")
    
    print("\n4. 训练命令:")
    print("   cd training/ppo")
    print("   python train_ppo_parking.py --benchmark 1")

if __name__ == "__main__":
    print("停车训练监控工具")
    print("用于观察改进后奖励函数的训练效果")
    
    analyze_reward_components()
    provide_training_suggestions()
    
    print("\n选择操作:")
    print("1. 开始监控训练 (需要训练正在进行)")
    print("2. 查看训练建议")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        monitor_training_progress()
    elif choice == "2":
        provide_training_suggestions()
    else:
        print("退出监控工具")
