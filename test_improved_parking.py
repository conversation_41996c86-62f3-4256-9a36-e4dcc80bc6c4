#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试改进后的停车奖励函数
验证智能体是否能够学会在目标附近刹车停车
"""

import warnings
import os
warnings.filterwarnings('ignore')
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

import numpy as np
import argparse
import sys
import traceback

# 添加项目路径
sys.path.append('.')

try:
    from algorithms.utils import get_configs
    from algorithms.environments import make_envs
    from algorithms.agents.utils.operations import set_seed
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")

def test_parking_behavior():
    """测试停车行为"""
    print("=== 测试改进后的停车奖励函数 ===")

    # 加载配置
    configs_dict = get_configs(file_dir="training/ppo/ppo_configs/test_ppo_parking.yaml")

    # 修复配置中的问题
    if configs_dict.get('seed') is None:
        configs_dict['seed'] = 42  # 设置默认种子

    configs_dict.update({
        'render': False,  # 先关闭渲染避免显示问题
        'render_mode': 'rgb_array',
        'test_episode': 3,
        'parallels': 1,
        'max_episode_steps': 200,
        'env_seed': 42,  # 确保环境种子也设置
        'test_mode': False,  # 设置为非测试模式以避免加载模型
        'benchmark': False
    })
    configs = argparse.Namespace(**configs_dict)
    
    # 初始化环境和智能体
    set_seed(configs.seed)
    envs = make_envs(configs)
    
    print("环境创建成功，开始测试...")
    
    # 手动测试几个回合
    for episode in range(3):
        print(f"\n--- 回合 {episode + 1} ---")
        
        obs, info = envs.reset()
        total_reward = 0
        step_count = 0
        
        for step in range(200):
            # 使用随机动作进行测试（可以替换为训练好的智能体）
            action = envs.action_space.sample()
            
            # 在接近目标时测试刹车行为
            if hasattr(envs, 'envs') and len(envs.envs) > 0:
                env = envs.envs[0]
                if hasattr(env, '_get_distance_to_goal'):
                    distance = env._get_distance_to_goal()
                    
                    # 当距离目标较近时，强制使用刹车动作进行测试
                    if distance < 5.0:
                        action[0] = -0.5  # 刹车
                        print(f"距离目标: {distance:.2f}m, 使用刹车动作: {action[0]:.2f}")
                    elif distance < 8.0:
                        action[0] = 0.1   # 慢速前进
                        print(f"距离目标: {distance:.2f}m, 使用慢速前进: {action[0]:.2f}")
            
            obs, reward, terminated, truncated, info = envs.step(action)
            total_reward += reward
            step_count += 1
            
            # 打印奖励分解信息
            if hasattr(info, '__len__') and len(info) > 0 and 'reward_breakdown' in info[0]:
                breakdown = info[0]['reward_breakdown']
                print(f"步骤 {step}: 总奖励={reward:.3f}, "
                      f"基础={breakdown.get('base_reward', 0):.3f}, "
                      f"路径={breakdown.get('path_reward', 0):.3f}, "
                      f"平滑={breakdown.get('smoothness_reward', 0):.3f}")
            
            if terminated or truncated:
                break
        
        print(f"回合结束: 总奖励={total_reward:.2f}, 步数={step_count}")
        
        # 检查最终状态
        if hasattr(envs, 'envs') and len(envs.envs) > 0:
            env = envs.envs[0]
            if hasattr(env, '_get_distance_to_goal'):
                final_distance = env._get_distance_to_goal()
                print(f"最终距离目标: {final_distance:.2f}m")
                
                if final_distance < 2.0:
                    print("✅ 成功接近目标！")
                elif final_distance < 5.0:
                    print("⚠️  较接近目标，需要进一步优化")
                else:
                    print("❌ 距离目标较远")

def analyze_reward_components():
    """分析奖励组件"""
    print("\n=== 奖励函数组件分析 ===")
    
    # 模拟不同距离下的奖励
    distances = [10.0, 8.0, 5.0, 3.0, 2.0, 1.0, 0.5]
    actions = [
        [0.5, 0.0],   # 正常前进
        [0.1, 0.0],   # 慢速前进
        [-0.2, 0.0],  # 轻微刹车
        [-0.5, 0.0],  # 强力刹车
        [0.0, 0.0],   # 停止
    ]
    
    print("距离(m) | 动作类型     | 期望行为")
    print("-" * 40)
    for dist in distances:
        print(f"{dist:6.1f} | ", end="")
        if dist > 8.0:
            print("正常前进     | 保持适中速度")
        elif dist > 5.0:
            print("准备减速     | 开始减速")
        elif dist > 3.0:
            print("减速接近     | 明显减速")
        elif dist > 1.5:
            print("准备停车     | 轻微前进或刹车")
        else:
            print("精确停车     | 停止或微调")

if __name__ == "__main__":
    print("改进后的停车奖励函数测试")
    print("主要改进:")
    print("1. 距离感知的速度控制")
    print("2. 停车精度奖励")
    print("3. 成功停车额外奖励")
    print("4. 分层距离奖励系统")
    
    try:
        analyze_reward_components()
        test_parking_behavior()
        
        print("\n=== 测试完成 ===")
        print("如果智能体仍然无法学会刹车，建议:")
        print("1. 增加停车精度奖励的权重")
        print("2. 调整距离阈值")
        print("3. 增加训练时间")
        print("4. 检查环境的终止条件")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        print("请检查环境配置和依赖项")
