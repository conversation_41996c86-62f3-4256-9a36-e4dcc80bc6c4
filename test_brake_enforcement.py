#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试强化刹车奖励函数
验证新的刹车强制机制是否有效
"""

import warnings
import os
warnings.filterwarnings('ignore')
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

import numpy as np
import sys
sys.path.append('.')

def test_brake_enforcement():
    """测试强化刹车奖励机制"""
    print("=== 测试强化刹车奖励机制 ===")
    
    try:
        from algorithms.environments.single_agent_env.custom_parking import CustomParkingEnv
        
        class SimpleConfig:
            def __init__(self):
                self.env_seed = 42
                self.max_episode_steps = 150
                self.render_mode = 'rgb_array'
        
        config = SimpleConfig()
        env = CustomParkingEnv(config)
        print("✅ 环境创建成功！")
        
        # 重置环境
        obs, info = env.reset()
        print("✅ 环境重置成功！")
        
        # 测试不同距离下的刹车奖励
        print("\n=== 测试强化刹车奖励 ===")
        
        # 模拟不同距离和动作的组合
        test_scenarios = [
            # (距离, 加速度, 描述)
            (15.0, 0.5, "远距离正常前进"),
            (10.0, 0.3, "10米中等前进"),
            (10.0, -0.1, "10米轻微刹车"),
            (7.0, 0.2, "7米慢速前进"),
            (7.0, -0.2, "7米明显刹车"),
            (5.0, 0.1, "5米微速前进"),
            (5.0, -0.1, "5米轻微刹车"),
            (3.0, 0.05, "3米极慢前进"),
            (3.0, -0.1, "3米刹车"),
            (3.0, 0.3, "3米快速前进（错误）"),
            (1.5, 0.0, "1.5米停止"),
            (1.5, -0.1, "1.5米刹车"),
            (1.0, 0.0, "1米精确停车"),
            (0.8, -0.05, "0.8米精确刹车"),
        ]
        
        print("距离(m) | 加速度  | 描述              | 刹车奖励 | 成功奖励 | 总奖励")
        print("-" * 80)
        
        for distance, accel, desc in test_scenarios:
            # 模拟设置距离（这里只是演示，实际环境中距离由车辆位置决定）
            action = np.array([accel, 0.0])
            
            try:
                # 执行动作
                obs, reward, terminated, truncated, info = env.step(action)
                
                # 获取奖励分解
                brake_reward = "N/A"
                success_reward = "N/A"
                
                if isinstance(info, dict) and 'reward_breakdown' in info:
                    breakdown = info['reward_breakdown']
                    # 这里需要在环境中添加刹车奖励的分解信息
                
                # 手动计算刹车奖励（用于验证）
                if hasattr(env, '_compute_brake_enforcement_reward'):
                    brake_reward = f"{env._compute_brake_enforcement_reward(action):.3f}"
                
                if hasattr(env, '_compute_parking_success_bonus'):
                    success_reward = f"{env._compute_parking_success_bonus(action):.3f}"
                
                print(f"{distance:7.1f} | {accel:7.2f} | {desc:16} | {brake_reward:8} | {success_reward:8} | {reward:7.3f}")
                
            except Exception as e:
                print(f"{distance:7.1f} | {accel:7.2f} | {desc:16} | 错误: {e}")
        
        # 测试连续刹车行为
        print(f"\n=== 测试连续刹车学习 ===")
        
        # 重置环境
        obs, info = env.reset()
        total_reward = 0
        brake_actions = 0
        
        for step in range(20):
            # 获取当前距离
            current_distance = 20.0  # 默认值
            if hasattr(env, '_get_distance_to_goal'):
                try:
                    current_distance = env._get_distance_to_goal()
                except:
                    pass
            
            # 根据距离选择动作（模拟智能体学习刹车）
            if current_distance < 3.0:
                action = np.array([-0.3, 0.0])  # 强力刹车
                brake_actions += 1
                action_desc = "强力刹车"
            elif current_distance < 5.0:
                action = np.array([-0.1, 0.0])  # 轻微刹车
                brake_actions += 1
                action_desc = "轻微刹车"
            elif current_distance < 8.0:
                action = np.array([0.1, 0.0])   # 慢速前进
                action_desc = "慢速前进"
            else:
                action = np.array([0.3, 0.0])   # 正常前进
                action_desc = "正常前进"
            
            obs, reward, terminated, truncated, info = env.step(action)
            total_reward += reward
            
            print(f"步骤 {step:2d}: 距离 {current_distance:5.1f}m | {action_desc:8} | 奖励 {reward:6.3f}")
            
            if terminated or truncated:
                print(f"回合结束于步骤 {step}")
                break
        
        print(f"\n总奖励: {total_reward:.3f}")
        print(f"刹车动作次数: {brake_actions}")
        print(f"刹车比例: {brake_actions/20*100:.1f}%")
        
        # 验证新方法存在
        print(f"\n=== 验证新增方法 ===")
        methods = ['_compute_brake_enforcement_reward', '_compute_parking_success_bonus']
        for method in methods:
            if hasattr(env, method):
                print(f"✅ {method} 存在")
            else:
                print(f"❌ {method} 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_improvements():
    """分析强化改进"""
    print("\n=== 强化刹车奖励改进分析 ===")
    
    print("\n🎯 新的奖励权重分配:")
    print("- 基础奖励: 15% (降低)")
    print("- 路径跟踪: 25% (降低)")
    print("- 平滑驾驶: 15% (降低)")
    print("- 停车精度: 25% (提高)")
    print("- 刹车强制: 20% (新增)")
    print("- 成功停车: 额外奖励 (强化)")
    
    print("\n🚨 强化刹车机制:")
    print("1. 扩大刹车区域到10米")
    print("2. 分层强制刹车要求:")
    print("   - 3米内: 必须刹车 (≤-0.1)")
    print("   - 5米内: 几乎停止 (≤0.05)")
    print("   - 7米内: 慢速 (≤0.15)")
    print("   - 10米内: 中等速度 (≤0.3)")
    
    print("\n💰 强化成功奖励:")
    print("- 1米内精确停车: +2.0 (超大奖励)")
    print("- 1.5米内停车: +1.5 (大奖励)")
    print("- 2米内停车: +1.0 (中等奖励)")
    print("- 3米内低速: +0.5 (基本奖励)")
    print("- 4米内刹车: +0.3 (刹车鼓励)")
    print("- 5米内低速: +0.2 (低速鼓励)")
    
    print("\n⚠️ 严厉惩罚机制:")
    print("- 5米内快速前进 (>0.2): -0.8")
    print("- 3米内任何加速 (>0.1): -1.0")

if __name__ == "__main__":
    print("强化刹车奖励函数测试")
    
    analyze_improvements()
    
    success = test_brake_enforcement()
    
    if success:
        print("\n🎉 强化刹车机制测试成功！")
        print("\n建议:")
        print("1. 立即重新训练智能体")
        print("2. 观察刹车行为是否出现")
        print("3. 监控强化刹车奖励的触发")
        print("4. 如果仍不刹车，考虑进一步提高刹车奖励权重")
    else:
        print("\n❌ 测试失败，请检查实现")
